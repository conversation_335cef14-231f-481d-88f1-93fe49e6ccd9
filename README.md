# 智慧婚恋服务平台后端

## 🎯 项目简介

智慧婚恋服务平台是一个基于Spring Boot 3.x + Java 21开发的现代化婚恋交友平台后端服务，采用企业级架构设计，集成了MariaDB、Redis、阿里巴巴Druid连接池等企业级组件，支持高并发、高可用的生产环境部署。

## 🏗️ 技术架构

### 核心技术栈
- **框架**: Spring Boot 3.5.5 + Java 21
- **数据库**: MariaDB + MyBatis
- **连接池**: 阿里巴巴 Druid（企业级优化配置）
- **缓存**: Redis + Lettuce
- **安全**: JWT + 国密SM2加密
- **文档**: Knife4j + OpenAPI 3
- **工具**: Hutool + Lombok

### 企业级特性
- ✅ **Java 21兼容性** - 完全支持最新Java特性
- ✅ **阿里巴巴Druid连接池** - 企业级数据库连接池监控
- ✅ **Redis集群支持** - 高可用缓存解决方案
- ✅ **JWT安全认证** - 无状态身份验证
- ✅ **国密SM2加密算法** - 符合国家密码标准
- ✅ **多线程池管理** - 异步任务、邮件、文件处理分离
- ✅ **Caffeine缓存** - 高性能本地缓存
- ✅ **配置外部化** - 支持yml配置和环境变量
- ✅ **统一异常处理** - 全局异常拦截和处理
- ✅ **API文档密码保护** - Knife4j文档安全访问
- ✅ **性能监控** - Actuator + Micrometer + Prometheus

## 🚀 快速开始

### 1. 环境要求
- Java 21+
- MariaDB 10.6+
- Redis 6.0+
- Maven 3.8+

### 2. 数据库配置

#### MariaDB配置
```bash
# 登录MariaDB
mysql -u root -p
# 密码: root123456

# 创建数据库
CREATE DATABASE blind_date CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### Redis配置
```bash
# Redis密码: StrongP@ssw0rd123
```

### 3. 启动项目

```bash
# 克隆项目
git clone <repository-url>
cd blind-date-backend

# 编译项目
mvn clean compile

# 启动项目
mvn spring-boot:run
```

### 4. 访问地址

| 服务 | 地址 | 用户名 | 密码 |
|------|------|--------|------|
| API文档 | http://localhost:8080/api/doc.html | admin | knife4j123 |
| Druid监控 | http://localhost:8080/api/druid/ | admin | admin123 |
| 系统健康检查 | http://localhost:8080/api/system/health | - | - |

## 📊 监控面板

### Druid数据库监控
- **功能**: 数据库连接池监控、SQL监控、Web应用监控
- **访问地址**: http://localhost:8080/api/druid/
- **登录信息**: admin / admin123

### API文档
- **功能**: 在线API文档、接口测试
- **访问地址**: http://localhost:8080/api/doc.html
- **登录信息**: admin / knife4j123

## 🔐 安全配置

### JWT认证
- **Token有效期**: 24小时
- **获取方式**: 通过登录接口获取
- **使用方式**: 请求头添加 `Authorization: Bearer {token}`

### 国密SM2加密
- **用途**: 敏感数据加密
- **配置**: 通过环境变量设置公私钥

## 📝 API使用说明

### 认证流程
1. 用户注册：`POST /api/auth/register`
2. 用户登录：`POST /api/auth/login`
3. 获取Token后，在后续请求中携带Token

### 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00"
}
```

### 错误码说明
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `500`: 服务器内部错误

## 🔧 配置说明

### 核心配置特性
- **配置外部化**: 所有自定义参数都可通过yml配置文件或环境变量设置
- **Java 21兼容**: 修复了Druid StatViewServlet的泛型兼容性问题
- **类型安全**: 使用@ConfigurationProperties确保配置类型安全
- **默认值支持**: 所有配置都有合理的默认值

### 主要配置模块

#### 1. 异步任务线程池配置
```yaml
async:
  task:                         # 通用异步任务
    core-pool-size: 8
    max-pool-size: 16
    queue-capacity: 100
  email:                        # 邮件发送专用
    core-pool-size: 2
    max-pool-size: 5
    queue-capacity: 50
  file:                         # 文件处理专用
    core-pool-size: 4
    max-pool-size: 8
    queue-capacity: 200
```

#### 2. 缓存配置
```yaml
cache:
  caffeine:
    maximum-size: 10000         # 最大缓存条目数
    expire-after-write-minutes: 10
    expire-after-access-minutes: 5
  user:                         # 用户专用缓存
    maximum-size: 5000
    expire-after-write-minutes: 5
```

#### 3. Druid监控配置
```yaml
druid:
  monitor:
    username: admin
    password: admin123
    allow: 127.0.0.1           # 允许访问的IP
  filter:
    exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
```

## 📁 项目结构

```
src/main/java/com/mbh/blinddate/
├── config/              # 配置类
│   ├── DruidConfig.java    # Druid配置
│   ├── RedisConfig.java    # Redis配置
│   └── Knife4jConfig.java  # API文档配置
├── controller/          # 控制器
├── service/            # 服务层
├── mapper/             # 数据访问层
├── entity/             # 实体类
├── dto/                # 数据传输对象
├── common/             # 公共类
├── util/               # 工具类
└── exception/          # 异常处理
```

## 🧪 测试

```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn integration-test

# 生成测试报告
mvn surefire-report:report
```

## 📦 部署

### Docker部署
```bash
# 构建镜像
docker build -t blind-date-backend .

# 运行容器
docker run -d -p 8080:8080 blind-date-backend
```

### 传统部署
```bash
# 打包
mvn clean package

# 运行
java -jar target/blind-date-0.0.1-SNAPSHOT.jar
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 📧 Email: <EMAIL>
- 🌐 Website: https://www.blinddate.com

---
*© 2024 盲盒交友系统 - 让缘分不再盲目*
