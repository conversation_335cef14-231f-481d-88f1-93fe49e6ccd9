package com.blinddate.service;

import com.mbh.blinddate.dto.LoginRequest;
import com.mbh.blinddate.dto.LoginResponse;
import com.mbh.blinddate.entity.User;
import com.mbh.blinddate.repository.UserRepository;
import com.mbh.blinddate.service.JwtService;
import com.mbh.blinddate.service.UserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Optional;
import java.security.MessageDigest;
import java.util.Base64;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class UserServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private JwtService jwtService;

    @InjectMocks
    private UserService userService;

    private User user;
    private LoginRequest loginRequest;
    private String hashedPassword;

    @BeforeEach
    void setUp() throws Exception {
        // Create test user
        user = new User();
        user.setId(1L);
        user.setUsername("testuser");
        // Hash the password "password123" and store it
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        hashedPassword = Base64.getEncoder().encodeToString(
            digest.digest("password123".getBytes()));
        user.setPassword(hashedPassword);
        user.setEmail("<EMAIL>");
        user.setNickname("Test User");
        user.setAvatar("avatar-url");

        loginRequest = new LoginRequest();
        loginRequest.setUsername("testuser");
        loginRequest.setPassword("password123");
    }

    @Test
    void loginSuccess() {
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(user));
        when(jwtService.generateToken(anyString())).thenReturn("test-token");

        LoginResponse response = userService.login(loginRequest);

        assertNotNull(response);
        assertEquals("test-token", response.getToken());
        assertEquals("testuser", response.getUsername());
        assertEquals("Test User", response.getNickname());
    }

    @Test
    void loginFailureInvalidUsername() {
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.empty());

        Exception exception = assertThrows(RuntimeException.class, 
            () -> userService.login(loginRequest));
        assertEquals("Invalid username or password", exception.getMessage());
    }

    @Test
    void loginFailureInvalidPassword() {
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(user));
        loginRequest.setPassword("wrongpassword");

        Exception exception = assertThrows(RuntimeException.class, 
            () -> userService.login(loginRequest));
        assertEquals("Invalid username or password", exception.getMessage());
    }

    @Test
    void registerSuccess() {
        when(userRepository.existsByUsername(anyString())).thenReturn(false);
        when(userRepository.existsByEmail(anyString())).thenReturn(false);
        when(userRepository.save(any(User.class))).thenReturn(user);

        User registeredUser = userService.register(user);

        assertNotNull(registeredUser);
        assertEquals("testuser", registeredUser.getUsername());
        assertEquals("<EMAIL>", registeredUser.getEmail());
    }

    @Test
    void registerFailureUsernameExists() {
        when(userRepository.existsByUsername(anyString())).thenReturn(true);

        Exception exception = assertThrows(RuntimeException.class, 
            () -> userService.register(user));
        assertEquals("Username already exists", exception.getMessage());
    }

    @Test
    void registerFailureEmailExists() {
        when(userRepository.existsByUsername(anyString())).thenReturn(false);
        when(userRepository.existsByEmail(anyString())).thenReturn(true);

        Exception exception = assertThrows(RuntimeException.class, 
            () -> userService.register(user));
        assertEquals("Email already exists", exception.getMessage());
    }
}
