package com.blinddate.service;

import com.mbh.blinddate.service.JwtService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

public class JwtServiceTest {

    private JwtService jwtService;
    private static final String TEST_USERNAME = "testuser";

    @BeforeEach
    void setUp() {
        jwtService = new JwtService();
        ReflectionTestUtils.setField(jwtService, "secret", "testSecretKey12345678901234567890");
        ReflectionTestUtils.setField(jwtService, "expiration", 3600000L);
        jwtService.init();
    }

    @Test
    void generateTokenAndValidate() {
        String token = jwtService.generateToken(TEST_USERNAME);

        assertNotNull(token);
        assertTrue(jwtService.validateToken(token));
    }

    @Test
    void getUsernameFromToken() {
        String token = jwtService.generateToken(TEST_USERNAME);
        String username = jwtService.getUsernameFromToken(token);

        assertEquals(TEST_USERNAME, username);
    }

    @Test
    void validateInvalidToken() {
        assertFalse(jwtService.validateToken("invalid.token.here"));
    }

    @Test
    void validateExpiredToken() {
        // Set a very short expiration time
        ReflectionTestUtils.setField(jwtService, "expiration", 1L);
        jwtService.init();
        
        String token = jwtService.generateToken(TEST_USERNAME);
        
        // Wait for token to expire
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        assertFalse(jwtService.validateToken(token));
    }
}
