# 盲盒交友系统 API 文档

## 🎯 项目简介

盲盒交友系统是一个基于Spring Boot 3.x开发的现代化交友平台后端服务，采用企业级架构设计，提供安全、高效、可扩展的API服务。

## 🏗️ 技术架构

### 核心技术栈
- **框架**: Spring Boot 3.5.5 + Java 21
- **数据库**: MariaDB + MyBatis
- **连接池**: 阿里巴巴 Druid（企业级优化配置）
- **缓存**: Redis + Lettuce
- **安全**: JWT + 国密SM2加密
- **文档**: Knife4j + OpenAPI 3
- **工具**: Hutool + Lombok

### 企业级特性
- ✅ 阿里巴巴Druid连接池监控
- ✅ Redis集群支持
- ✅ JWT安全认证
- ✅ 国密SM2加密算法
- ✅ 统一异常处理
- ✅ 参数校验
- ✅ API文档密码保护

## 🔐 安全认证

本系统采用JWT Bearer Token认证方式：

1. 通过登录接口获取Token
2. 在请求头中添加：`Authorization: Bearer {token}`
3. Token有效期：24小时

## 📊 监控面板

### Druid数据库监控
- **访问地址**: `/druid/`
- **用户名**: admin
- **密码**: admin123

### API文档访问
- **访问地址**: `/doc.html`
- **用户名**: admin  
- **密码**: knife4j123

## 🚀 快速开始

### 1. 环境要求
- Java 21+
- MariaDB 10.6+
- Redis 6.0+

### 2. 数据库配置
```yaml
spring:
  datasource:
    url: ****************************************
    username: root
    password: root123456
```

### 3. Redis配置
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      password: StrongP@ssw0rd123
```

## 📝 API使用说明

### 认证流程
1. 用户注册：`POST /auth/register`
2. 用户登录：`POST /auth/login`
3. 获取Token后，在后续请求中携带Token

### 主要功能模块
- **用户管理**: 注册、登录、个人信息管理
- **用户设置**: 隐私设置、通知设置
- **文件上传**: 头像、照片上传
- **匹配系统**: 用户匹配、推荐算法
- **消息系统**: 实时聊天、消息推送

## 🔧 开发指南

### 响应格式
所有API响应均采用统一格式：
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00"
}
```

### 错误码说明
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `500`: 服务器内部错误

## 📞 联系我们

如有问题或建议，请联系开发团队：
- 📧 Email: <EMAIL>
- 🌐 Website: https://www.blinddate.com

---
*© 2024 盲盒交友系统 - 让缘分不再盲目*
