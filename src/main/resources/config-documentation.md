# 智慧婚恋服务平台配置文档

## 配置参数说明

### 1. Druid 数据库监控配置

```yaml
druid:
  monitor:
    username: admin              # 监控页面用户名
    password: admin123           # 监控页面密码
    allow: 127.0.0.1            # 允许访问的IP，空表示允许所有
    deny:                       # 拒绝访问的IP，优先级高于allow
  filter:
    exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*,/actuator/*"  # 不统计的请求
```

**访问地址**: http://localhost:8080/api/druid/

### 2. 异步任务线程池配置

```yaml
async:
  task:                         # 通用异步任务线程池
    core-pool-size: 8           # 核心线程数
    max-pool-size: 16           # 最大线程数
    queue-capacity: 100         # 队列容量
    keep-alive-seconds: 60      # 线程空闲时间(秒)
    await-termination-seconds: 60  # 等待终止时间(秒)
  email:                        # 邮件发送线程池
    core-pool-size: 2
    max-pool-size: 5
    queue-capacity: 50
  file:                         # 文件处理线程池
    core-pool-size: 4
    max-pool-size: 8
    queue-capacity: 200
```

### 3. 缓存配置

```yaml
cache:
  caffeine:                     # 通用缓存配置
    maximum-size: 10000         # 最大缓存条目数
    expire-after-write-minutes: 10    # 写入后过期时间(分钟)
    expire-after-access-minutes: 5    # 访问后过期时间(分钟)
    initial-capacity: 100       # 初始容量
  user:                         # 用户缓存配置
    maximum-size: 5000
    expire-after-write-minutes: 5
  config:                       # 配置缓存
    maximum-size: 1000
    expire-after-write-hours: 1 # 写入后过期时间(小时)
```

### 4. 事务配置

```yaml
transaction:
  default-timeout: 30           # 默认事务超时时间(秒)
  global-rollback-on-participation-failure: false  # 参与失败时全局回滚
```

## 性能调优建议

### 生产环境推荐配置

#### 高并发场景 (QPS > 1000)
```yaml
async:
  task:
    core-pool-size: 16
    max-pool-size: 32
    queue-capacity: 200

cache:
  caffeine:
    maximum-size: 50000
    expire-after-write-minutes: 30
```

#### 内存受限场景
```yaml
cache:
  caffeine:
    maximum-size: 5000
    expire-after-write-minutes: 5
  user:
    maximum-size: 2000
    expire-after-write-minutes: 3
```

#### 数据库连接池优化
```yaml
spring:
  datasource:
    druid:
      initial-size: 20
      max-active: 100
      min-idle: 20
```

## 监控指标

### 1. 线程池监控
- 活跃线程数
- 队列长度
- 任务完成数
- 拒绝任务数

### 2. 缓存监控
- 命中率
- 缓存大小
- 驱逐次数
- 加载时间

### 3. 数据库监控
- 连接池使用率
- SQL执行时间
- 慢查询统计

## 故障排查

### 常见问题

1. **线程池满载**
   - 现象：任务执行缓慢，大量任务排队
   - 解决：增加 `max-pool-size` 或 `queue-capacity`

2. **缓存命中率低**
   - 现象：数据库压力大，响应慢
   - 解决：调整 `expire-after-write-minutes` 或增加 `maximum-size`

3. **数据库连接超时**
   - 现象：获取连接失败
   - 解决：增加 `max-active` 或检查连接泄漏

### 日志配置

```yaml
logging:
  level:
    com.mbh.blinddate.config: DEBUG
    com.alibaba.druid: INFO
    org.springframework.cache: DEBUG
```

## 环境变量支持

支持通过环境变量覆盖配置：

```bash
# 异步任务配置
export ASYNC_TASK_CORE_POOL_SIZE=16
export ASYNC_TASK_MAX_POOL_SIZE=32

# 缓存配置
export CACHE_CAFFEINE_MAXIMUM_SIZE=20000

# Druid监控配置
export DRUID_MONITOR_USERNAME=production_admin
export DRUID_MONITOR_PASSWORD=secure_password_123
```

## 配置验证

系统启动时会自动验证配置参数的合理性：
- 线程池参数：core-pool-size <= max-pool-size
- 缓存参数：maximum-size > 0
- 超时参数：timeout > 0

不合理的配置会在启动时输出警告日志。
