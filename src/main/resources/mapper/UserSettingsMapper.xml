<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mbh.blinddate.mapper.UserSettingsMapper">
    <resultMap id="userSettingsMap" type="com.mbh.blinddate.entity.UserSettings">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="minAge" column="min_age"/>
        <result property="maxAge" column="max_age"/>
        <result property="preferredGender" column="preferred_gender"/>
        <result property="preferredLocation" column="preferred_location"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <select id="findById" resultMap="userSettingsMap">
        SELECT * FROM user_settings WHERE id = #{id}
    </select>

    <select id="findByUserId" resultMap="userSettingsMap">
        SELECT * FROM user_settings WHERE user_id = #{userId}
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_settings (user_id, min_age, max_age, preferred_gender, preferred_location, created_at, updated_at)
        VALUES (#{userId}, #{minAge}, #{maxAge}, #{preferredGender}, #{preferredLocation}, NOW(), NOW())
    </insert>

    <update id="update">
        UPDATE user_settings
        <set>
            <if test="minAge != null">min_age = #{minAge},</if>
            <if test="maxAge != null">max_age = #{maxAge},</if>
            <if test="preferredGender != null">preferred_gender = #{preferredGender},</if>
            <if test="preferredLocation != null">preferred_location = #{preferredLocation},</if>
            updated_at = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM user_settings WHERE id = #{id}
    </delete>
</mapper>
