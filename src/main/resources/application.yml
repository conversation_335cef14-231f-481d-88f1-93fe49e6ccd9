spring:
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:blinddate;MODE=MySQL;DB_CLOSE_DELAY=-1;DATABASE_TO_LOWER=TRUE
    username: sa
    password: password
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: true
  sql:
    init:
      mode: always
      schema-locations: classpath:schema.sql
      data-locations: classpath:data.sql

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.mbh.blinddate.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

server:
  port: 8080
  servlet:
    context-path: /api

jwt:
  secret: your-secret-key-here-make-it-very-long-and-secure-in-production
  expiration: 86400000  # 24 hours in milliseconds

redis:
  host: localhost
  port: 6379
  password:

# 添加阿里云 OSS 配置
aliyun:
  oss:
    endpoint: oss-cn-hangzhou.aliyuncs.com
    accessKeyId: your-access-key-id
    accessKeySecret: your-access-key-secret
    bucketName: your-bucket-name
    urlPrefix: https://your-bucket-name.oss-cn-hangzhou.aliyuncs.com/

# 文件上传配置
upload:
  path: ./uploads  # 本地文件上传路径
  allowTypes: jpg,jpeg,png,gif  # 允许的文件类型
  maxSize: 5242880  # 最大文件大小（5MB）

# 加密配置
crypto:
  enabled: true  # 启用加密功能
  sm2:
    private-key: ${SM2_PRIVATE_KEY:}  # 从环境变量获取，默认为空
    public-key: ${SM2_PUBLIC_KEY:}    # 从环境变量获取，默认为空
