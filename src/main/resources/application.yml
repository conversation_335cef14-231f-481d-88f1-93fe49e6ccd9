spring:
  # MariaDB 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.mariadb.jdbc.Driver
    url: **********************************************************************************************************************************************************
    username: root
    password: root123456

    # Druid 连接池配置 - 企业级优化
    druid:
      # 初始连接数
      initial-size: 10
      # 最小连接池数量
      min-idle: 10
      # 最大连接池数量
      max-active: 200
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      max-evictable-idle-time-millis: 900000
      # 用来检测连接是否有效的sql，要求是一个查询语句
      validation-query: SELECT 1
      # 建议配置为true，不影响性能，并且保证安全性
      test-while-idle: true
      # 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      test-on-borrow: false
      # 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      test-on-return: false
      # 是否缓存preparedStatement，也就是PSCache
      pool-prepared-statements: true
      # 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      # 合并多个DruidDataSource的监控数据
      use-global-data-source-stat: true

      # Web监控配置
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"

      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin123
        allow: 127.0.0.1

  # Redis 配置
  data:
    redis:
      host: *************
      port: 6379
      password: StrongP@ssw0rd123
      timeout: 6000ms
      database: 0
      lettuce:
        pool:
          max-active: 1000
          max-wait: -1ms
          max-idle: 10
          min-idle: 5

  sql:
    init:
      mode: never  # 生产环境建议设置为never

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.mbh.blinddate.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

server:
  port: 8080
  servlet:
    context-path: /api

jwt:
  secret: your-secret-key-here-make-it-very-long-and-secure-in-production
  expiration: 86400000  # 24 hours in milliseconds

# 移除旧的redis配置，已在spring.data.redis中配置

# 添加阿里云 OSS 配置
aliyun:
  oss:
    endpoint: oss-cn-hangzhou.aliyuncs.com
    accessKeyId: your-access-key-id
    accessKeySecret: your-access-key-secret
    bucketName: your-bucket-name
    urlPrefix: https://your-bucket-name.oss-cn-hangzhou.aliyuncs.com/

# 文件上传配置
upload:
  path: ./uploads  # 本地文件上传路径
  allowTypes: jpg,jpeg,png,gif  # 允许的文件类型
  maxSize: 5242880  # 最大文件大小（5MB）

# 加密配置
crypto:
  enabled: true  # 启用加密功能
  sm2:
    private-key: ${SM2_PRIVATE_KEY:}  # 从环境变量获取，默认为空
    public-key: ${SM2_PUBLIC_KEY:}    # 从环境变量获取，默认为空

# Knife4j 配置
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-version: true
    enable-reload-cache-parameter: true
    enable-after-script: true
    enable-filter-multipart-api-method-type: POST
    enable-filter-multipart-apis: false
    enable-request-cache: true
    enable-host: false
    enable-host-text:
    enable-home-custom: true
    home-custom-path: classpath:markdown/home.md
    enable-search: true
    enable-footer: false
    enable-footer-custom: true
    footer-custom-content: Copyright © 2024 盲盒交友系统
    enable-dynamic-parameter: true
    enable-debug: true
    enable-open-api: false
    enable-group: true
  cors: false
  production: false
  basic:
    enable: true
    username: admin
    password: knife4j123
