spring:
  # MariaDB 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.mariadb.jdbc.Driver
    url: jdbc:mariadb://*************:3306/blind_date?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai&useSSL=false&allowPublicKeyRetrieval=true
    username: root
    password: root123456

    # Druid 连接池配置 - 企业级优化
    druid:
      # 初始连接数
      initial-size: 10
      # 最小连接池数量
      min-idle: 10
      # 最大连接池数量
      max-active: 200
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      max-evictable-idle-time-millis: 900000
      # 用来检测连接是否有效的sql，要求是一个查询语句
      validation-query: SELECT 1
      # 建议配置为true，不影响性能，并且保证安全性
      test-while-idle: true
      # 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      test-on-borrow: false
      # 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      test-on-return: false
      # 是否缓存preparedStatement，也就是PSCache
      pool-prepared-statements: true
      # 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      # 合并多个DruidDataSource的监控数据
      use-global-data-source-stat: true
      stat-view-servlet:
        enabled: true
        login-username: admin
        login-password: admin123
        url-pattern: /druid/*

      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*,/actuator/*"

  # Redis 配置
  data:
    redis:
      host: *************
      port: 6379
      password: StrongP@ssw0rd123
      timeout: 6000ms
      database: 0
      lettuce:
        pool:
          max-active: 1000
          max-wait: -1ms
          max-idle: 10
          min-idle: 5

  sql:
    init:
      mode: never  # 生产环境建议设置为never

  # Jackson 序列化优化 - 避免时区问题
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
      indent-output: false
    deserialization:
      fail-on-unknown-properties: false

  # 文件上传限制优化
  servlet:
    multipart:
      max-file-size: 100MB          # 单文件最大大小
      max-request-size: 100MB       # 总请求最大大小
      file-size-threshold: 2KB      # 文件写入磁盘的阈值
      location: /tmp                # 临时文件存储位置
      resolve-lazily: false         # 是否延迟解析

  # 异步任务线程池配置 - 避免使用默认的SimpleAsyncTaskExecutor
  task:
    execution:
      pool:
        core-size: 8                # 核心线程数
        max-size: 16               # 最大线程数
        queue-capacity: 100        # 队列容量
        keep-alive: 60s            # 线程空闲时间
      thread-name-prefix: async-task-
    scheduling:
      pool:
        size: 4                    # 定时任务线程池大小
      thread-name-prefix: scheduling-

  # 静态资源缓存优化
  web:
    resources:
      cache:
        cachecontrol:
          max-age: 365d            # 缓存时间
          cache-public: true       # 公共缓存
      chain:
        strategy:
          content:
            enabled: true          # 启用内容版本策略
            paths: /**
        cache: true                # 启用资源链缓存
      static-locations: classpath:/static/

  # 缓存配置优化 - 避免使用默认的ConcurrentHashMap
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=10000,expireAfterWrite=600s,recordStats
    cache-names:
      - userCache
      - tokenCache
      - configCache
  application:
    name: ${application-name:blind-date}

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.mbh.blinddate.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 开启二级缓存
    cache-enabled: true
    # 延迟加载配置
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
    # 批量执行器
    default-executor-type: REUSE

server:
  port: ${server.port:8080}
  servlet:
    context-path: ${context.path:/bind-date}
  # Tomcat 连接池优化 - 避免高并发下请求排队
  tomcat:
    max-connections: 10000          # 最大连接数
    threads:
      max: 800                      # 最大线程数
      min-spare: 100               # 最小空闲线程数
    accept-count: 100               # 等待队列长度
    connection-timeout: 20000       # 连接超时时间(ms)
    max-http-form-post-size: 100MB  # 表单提交最大大小


# 日志配置优化 - 避免无日志滚动/清理
logging:
  level:
    com.mbh.blinddate: INFO
    org.springframework.web: INFO
    org.mybatis: INFO
    com.alibaba.druid: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/app.log
  logback:
    rollingpolicy:
      max-file-size: 100MB        # 单个日志文件最大大小
      max-history: 30             # 保留30天的日志
      total-size-cap: 3GB         # 日志文件总大小限制
      clean-history-on-start: true # 启动时清理历史日志


# Actuator 监控端点配置 - 避免暴露敏感信息
management:
  endpoints:
    web:
      base-path: /manage # 修改默认路径，避免被扫描
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    # 健康检查
    health:
      show-details: when-authorized
      show-components: when-authorized
    info: # 应用信息
      access: read_only
    metrics: # 指标信息
      access: read_only
  info: # 应用信息配置
    env:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true
    git:
      enabled: true
      mode: simple

# 自定义 info 端点内容
info:
  app:
    name: ${spring.application.name}
    version: "@project.version@"
    description: "@project.description@"
  env: true        # 是否显示环境信息（Spring Boot 3.5.5 推荐这样写）
  java: true       # 是否显示 Java 运行信息
  os: true         # 是否显示操作系统信息




# 异步任务线程池配置
async:
  task:
    core-pool-size: 8
    max-pool-size: 16
    queue-capacity: 100
    keep-alive-seconds: 60
    await-termination-seconds: 60
  email:
    core-pool-size: 2
    max-pool-size: 5
    queue-capacity: 50
  file:
    core-pool-size: 4
    max-pool-size: 8
    queue-capacity: 200

# 缓存配置
cache:
  caffeine:
    maximum-size: 10000
    expire-after-write-minutes: 10
    expire-after-access-minutes: 5
    initial-capacity: 100
  user:
    maximum-size: 5000
    expire-after-write-minutes: 5
  config:
    maximum-size: 1000
    expire-after-write-hours: 1

# 事务配置
transaction:
  default-timeout: 30
  global-rollback-on-participation-failure: false


# Knife4j 配置
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-version: true
    enable-reload-cache-parameter: true
    enable-after-script: true
    enable-filter-multipart-api-method-type: POST
    enable-filter-multipart-apis: false
    enable-request-cache: true
    enable-host: false
    enable-host-text:
    enable-home-custom: true
    home-custom-path: classpath:markdown/home.md
    enable-search: true
    enable-footer: false
    enable-footer-custom: true
    footer-custom-content: Copyright © 2024 智慧婚恋服务平台
    enable-dynamic-parameter: true
    enable-debug: true
    enable-open-api: false
    enable-group: true
  cors: false
  production: false
  basic:
    enable: true
    username: admin
    password: knife4j123
