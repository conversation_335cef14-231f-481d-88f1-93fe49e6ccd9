package com.mbh.blinddate.controller;

import com.mbh.blinddate.dto.EmailUpdateDTO;
import com.mbh.blinddate.dto.PasswordUpdateDTO;
import com.mbh.blinddate.dto.PhoneUpdateDTO;
import com.mbh.blinddate.dto.UserProfileDTO;
import com.mbh.blinddate.entity.User;
import com.mbh.blinddate.service.UserService;
import com.mbh.blinddate.util.CurrentContext;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    /**
     * 获取当前用户个人资料
     */
    @GetMapping("/profile")
    public ResponseEntity<User> getUserProfile() {
        Long userId = CurrentContext.getCurrentUserId();
        return ResponseEntity.ok(userService.getUserProfile(userId));
    }

    /**
     * 更新个人资料
     */
    @PutMapping("/profile")
    public ResponseEntity<Void> updateProfile(@Valid @RequestBody UserProfileDTO profileDTO) {
        Long userId = CurrentContext.getCurrentUserId();
        userService.updateProfile(userId, profileDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * 更新头像
     */
    @PutMapping("/avatar")
    public ResponseEntity<String> updateAvatar(@RequestParam("file") MultipartFile file) {
        Long userId = CurrentContext.getCurrentUserId();
        String avatarUrl = userService.updateAvatar(userId, file);
        return ResponseEntity.ok(avatarUrl);
    }

    /**
     * 修改密码
     */
    @PutMapping("/password")
    public ResponseEntity<Void> updatePassword(@Valid @RequestBody PasswordUpdateDTO passwordDTO) {
        Long userId = CurrentContext.getCurrentUserId();
        userService.updatePassword(userId, passwordDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * 发送验证码
     */
    @PostMapping("/verify-code")
    public ResponseEntity<Void> sendVerificationCode(@RequestParam String target, @RequestParam String type) {
        userService.sendVerificationCode(target, type);
        return ResponseEntity.ok().build();
    }

    /**
     * 更新手机号
     */
    @PutMapping("/phone")
    public ResponseEntity<Void> updatePhone(@Valid @RequestBody PhoneUpdateDTO phoneDTO) {
        Long userId = CurrentContext.getCurrentUserId();
        userService.updatePhone(userId, phoneDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * 更新邮箱
     */
    @PutMapping("/email")
    public ResponseEntity<Void> updateEmail(@Valid @RequestBody EmailUpdateDTO emailDTO) {
        Long userId = CurrentContext.getCurrentUserId();
        userService.updateEmail(userId, emailDTO);
        return ResponseEntity.ok().build();
    }
}
