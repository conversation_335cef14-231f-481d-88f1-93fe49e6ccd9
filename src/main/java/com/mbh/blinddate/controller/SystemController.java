package com.mbh.blinddate.controller;

import com.mbh.blinddate.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统监控控制器
 * 
 * <AUTHOR>
 */
@Tag(name = "系统监控", description = "系统状态监控相关接口")
@RestController
@RequestMapping("/system")
public class SystemController {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Operation(summary = "系统健康检查", description = "检查数据库和Redis连接状态")
    @GetMapping("/health")
    public Result<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        // 检查数据库连接
        try (Connection connection = dataSource.getConnection()) {
            health.put("database", "connected");
            health.put("database_url", connection.getMetaData().getURL());
            health.put("database_driver", connection.getMetaData().getDriverName());
        } catch (Exception e) {
            health.put("database", "disconnected");
            health.put("database_error", e.getMessage());
        }
        
        // 检查Redis连接
        try {
            redisTemplate.opsForValue().set("health_check", "ok");
            String value = (String) redisTemplate.opsForValue().get("health_check");
            if ("ok".equals(value)) {
                health.put("redis", "connected");
            } else {
                health.put("redis", "error");
            }
            redisTemplate.delete("health_check");
        } catch (Exception e) {
            health.put("redis", "disconnected");
            health.put("redis_error", e.getMessage());
        }
        
        health.put("status", "running");
        health.put("timestamp", System.currentTimeMillis());
        
        return Result.success(health);
    }

    @Operation(summary = "系统信息", description = "获取系统基本信息")
    @GetMapping("/info")
    public Result<Map<String, Object>> systemInfo() {
        Map<String, Object> info = new HashMap<>();
        
        // JVM信息
        Runtime runtime = Runtime.getRuntime();
        info.put("java_version", System.getProperty("java.version"));
        info.put("java_vendor", System.getProperty("java.vendor"));
        info.put("os_name", System.getProperty("os.name"));
        info.put("os_version", System.getProperty("os.version"));
        
        // 内存信息
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        
        Map<String, Object> memory = new HashMap<>();
        memory.put("total", totalMemory / 1024 / 1024 + " MB");
        memory.put("free", freeMemory / 1024 / 1024 + " MB");
        memory.put("used", (totalMemory - freeMemory) / 1024 / 1024 + " MB");
        memory.put("max", maxMemory / 1024 / 1024 + " MB");
        
        info.put("memory", memory);
        info.put("processors", runtime.availableProcessors());
        info.put("timestamp", System.currentTimeMillis());
        
        return Result.success(info);
    }
}
