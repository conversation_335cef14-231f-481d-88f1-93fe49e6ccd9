package com.mbh.blinddate.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mbh.blinddate.dto.DecryptedLoginData;
import com.mbh.blinddate.dto.EncryptedLoginRequest;
import com.mbh.blinddate.dto.LoginResponse;
import com.mbh.blinddate.dto.RegisterDTO;
import com.mbh.blinddate.service.UserService;
import com.mbh.blinddate.util.CryptoUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final UserService userService;
    
    @Autowired(required = false)
    private CryptoUtil cryptoUtil;

    private final ObjectMapper objectMapper;

    @PostMapping("/register")
    public ResponseEntity<Void> register(@RequestBody RegisterDTO registerDTO) {
        userService.register(registerDTO);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/login")
    public ResponseEntity<LoginResponse> login(@RequestBody EncryptedLoginRequest request) {
        try {
            DecryptedLoginData loginData;
            if (cryptoUtil != null) {
                // 使用 AES 解密（而不是 SM4）
                String decryptedJson = cryptoUtil.aesDecrypt(request.getEncryptedData(), request.getKey());
                loginData = objectMapper.readValue(decryptedJson, DecryptedLoginData.class);
                
                // 验证时间戳
                if (!cryptoUtil.isTimestampValid(loginData.getTimestamp())) {
                    throw new RuntimeException("登录请求已过期");
                }
            } else {
                // 非加密模式，直接解析JSON
                loginData = objectMapper.readValue(request.getEncryptedData(), DecryptedLoginData.class);
            }
            
            // 执行登录
            LoginResponse response = userService.login(loginData.getUsername(), loginData.getPassword());
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            throw new RuntimeException("登录失败: " + e.getMessage());
        }
    }
}
