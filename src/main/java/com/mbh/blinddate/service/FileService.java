package com.mbh.blinddate.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.mbh.blinddate.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.UUID;

@Slf4j
@Service
@ConditionalOnProperty(prefix = "aliyun.oss", name = "enabled", havingValue = "true", matchIfMissing = false)
public class FileService {

    @Value("${aliyun.oss.endpoint}")
    private String endpoint;

    @Value("${aliyun.oss.accessKeyId}")
    private String accessKeyId;

    @Value("${aliyun.oss.accessKeySecret}")
    private String accessKeySecret;

    @Value("${aliyun.oss.bucketName}")
    private String bucketName;

    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    private static final String[] ALLOWED_IMAGE_TYPES = {"jpg", "jpeg", "png", "gif"};

    /**
     * 上传图片文件到OSS
     * @param file 图片文件
     * @param directory 存储目录
     * @return 文件访问URL
     */
    public String uploadImage(MultipartFile file, String directory) {
        // 验证文件
        validateImageFile(file);

        try {
            // 生成文件名
            String fileName = generateFileName(file);
            String objectName = directory + "/" + fileName;

            // 压缩图片
            byte[] compressedImageData = compressImage(file);

            // 设置文件元信息
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(file.getContentType());
            metadata.setContentLength(compressedImageData.length);

            // 创建OSS客户端
            OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

            try {
                // 上传文件
                PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectName,
                        new ByteArrayInputStream(compressedImageData), metadata);
                ossClient.putObject(putObjectRequest);

                // 生成文件访问URL
                return String.format("https://%s.%s/%s", bucketName, endpoint, objectName);
            } finally {
                ossClient.shutdown();
            }
        } catch (IOException e) {
            log.error("图片上传失败", e);
            throw new BusinessException("图片上传失败");
        }
    }

    /**
     * 验证图片文件
     */
    private void validateImageFile(MultipartFile file) {
        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new BusinessException("文件大小不能超过5MB");
        }

        // 检查文件类型
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        boolean isValidType = false;
        for (String type : ALLOWED_IMAGE_TYPES) {
            if (type.equalsIgnoreCase(extension)) {
                isValidType = true;
                break;
            }
        }
        if (!isValidType) {
            throw new BusinessException("只支持jpg、jpeg、png、gif格式的图片");
        }
    }

    /**
     * 生成唯一文件名
     */
    private String generateFileName(MultipartFile file) {
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        return UUID.randomUUID().toString().replace("-", "") + "." + extension;
    }

    /**
     * 压缩图片
     */
    private byte[] compressImage(MultipartFile file) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Thumbnails.of(file.getInputStream())
                .size(800, 800) // 设置最大尺寸
                .outputQuality(0.8) // 设置压缩质量
                .toOutputStream(outputStream);
        return outputStream.toByteArray();
    }
}
