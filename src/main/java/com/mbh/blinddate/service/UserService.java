package com.mbh.blinddate.service;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.mbh.blinddate.dto.EmailUpdateDTO;
import com.mbh.blinddate.dto.LoginResponse;
import com.mbh.blinddate.dto.PasswordUpdateDTO;
import com.mbh.blinddate.dto.PhoneUpdateDTO;
import com.mbh.blinddate.dto.RegisterDTO;
import com.mbh.blinddate.dto.UserProfileDTO;
import com.mbh.blinddate.entity.User;
import com.mbh.blinddate.exception.BusinessException;
import com.mbh.blinddate.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

    private final UserMapper userMapper;
    private final JwtService jwtService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    @Autowired(required = false)
    private FileService fileService;
    
    private static final String VERIFY_CODE_PREFIX = "verify_code:";
    private static final long VERIFY_CODE_EXPIRE = 5; // 验证码有效期（分钟）
    private static final int MAX_LOGIN_ATTEMPTS = 5; // 最大登录尝试次数
    private static final long LOCK_DURATION = 30; // 账号锁定时间（分钟）
    private static final String LOGIN_FAIL_COUNT_PREFIX = "login:fail:count:";
    private static final String LOGIN_LOCK_PREFIX = "login:lock:";
    private static final int MAX_FAIL_COUNT = 5;
    private static final long LOCK_TIME_MINUTES = 30;

    /**
     * 注册新用户
     */
    @Transactional
    public User register(RegisterDTO registerDTO) {
        // 验证用户名是否已存在
        if (userMapper.existsByUsername(registerDTO.getUsername())) {
            throw new BusinessException("用户名已存在");
        }
        
        // 验证邮箱是否已存在
        if (StrUtil.isNotBlank(registerDTO.getEmail()) && userMapper.existsByEmail(registerDTO.getEmail())) {
            throw new BusinessException("邮箱已被使用");
        }
        
        // 验证手机号是否已存在
        if (StrUtil.isNotBlank(registerDTO.getPhone()) && userMapper.existsByPhone(registerDTO.getPhone())) {
            throw new BusinessException("手机号已被使用");
        }
        
        // 创建新用户
        User user = new User();
        BeanUtils.copyProperties(registerDTO, user);
        user.setPassword(BCrypt.hashpw(registerDTO.getPassword()));
        user.setRole("USER");
        user.setStatus(1);
        
        userMapper.insert(user);
        return user;
    }

    /**
     * 获取用户个人资料
     */
    public User getUserProfile(Long userId) {
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        return user;
    }

    /**
     * 更新用户个人资料
     */
    @Transactional
    public void updateProfile(Long userId, UserProfileDTO profileDTO) {
        User user = getUserProfile(userId);
        
        // 如果要更新邮箱，检查是否已被使用
        if (profileDTO.getEmail() != null && !profileDTO.getEmail().equals(user.getEmail())) {
            if (userMapper.findByEmail(profileDTO.getEmail()) != null) {
                throw new BusinessException("该邮箱已被使用");
            }
        }
        
        // 如果要更新手机号，检查是否已被使用
        if (profileDTO.getPhone() != null && !profileDTO.getPhone().equals(user.getPhone())) {
            if (userMapper.findByPhone(profileDTO.getPhone()) != null) {
                throw new BusinessException("该手机号已被使用");
            }
        }
        
        BeanUtils.copyProperties(profileDTO, user);
        userMapper.update(user);
    }

    /**
     * 更新用户头像
     */
    @Transactional
    public String updateAvatar(Long userId, MultipartFile file) {
        if (fileService == null) {
            throw new BusinessException("文件上传服务未启用");
        }
        User user = getUserProfile(userId);
        String avatarUrl = fileService.uploadImage(file, "avatars");
        user.setAvatar(avatarUrl);
        userMapper.update(user);
        return avatarUrl;
    }

    /**
     * 修改密码
     */
    @Transactional
    public void updatePassword(Long userId, PasswordUpdateDTO passwordDTO) {
        User user = getUserProfile(userId);
        
        // 验证旧密码
        if (!BCrypt.checkpw(passwordDTO.getOldPassword(), user.getPassword())) {
            throw new BusinessException("当前密码错误");
        }
        
        // 验证新密码与确认密码是否一致
        if (!passwordDTO.getNewPassword().equals(passwordDTO.getConfirmPassword())) {
            throw new BusinessException("两次输入的密码不一致");
        }
        
        // 更新密码
        user.setPassword(BCrypt.hashpw(passwordDTO.getNewPassword()));
        userMapper.update(user);
    }

    /**
     * 发送验证码
     */
    public void sendVerificationCode(String target, String type) {
        // 生成6位数字验证码
        String code = RandomUtil.randomNumbers(6);
        String key = VERIFY_CODE_PREFIX + type + ":" + target;
        
        // 检查是否频繁发送
        String existingCode = (String) redisTemplate.opsForValue().get(key);
        if (existingCode != null) {
            throw new BusinessException("验证码已发送，请稍后再试");
        }
        
        // 保存验证码到Redis
        redisTemplate.opsForValue().set(key, code, VERIFY_CODE_EXPIRE, TimeUnit.MINUTES);
        
        // 发送验证码
        if ("email".equals(type)) {
            // TODO: 调用邮件服务发送验证码
            log.info("发送邮件验证码: {}", code);
        } else if ("phone".equals(type)) {
            // TODO: 调用短信服务发送验证码
            log.info("发送短信验证码: {}", code);
        }
    }

    /**
     * 验证验证码
     */
    private void verifyCode(String target, String type, String code) {
        String key = VERIFY_CODE_PREFIX + type + ":" + target;
        String savedCode = (String) redisTemplate.opsForValue().get(key);
        
        if (savedCode == null) {
            throw new BusinessException("验证码已过期");
        }
        
        if (!savedCode.equals(code)) {
            throw new BusinessException("验证码错误");
        }
        
        // 验证成功后删除验证码
        redisTemplate.delete(key);
    }

    /**
     * 更新手机号
     */
    @Transactional
    public void updatePhone(Long userId, PhoneUpdateDTO phoneDTO) {
        // 验证验证码
        verifyCode(phoneDTO.getPhone(), "phone", phoneDTO.getCode());
        
        // 检查手机号是否已被使用
        if (userMapper.findByPhone(phoneDTO.getPhone()) != null) {
            throw new BusinessException("该手机号已被使用");
        }
        
        // 更新手机号
        User user = getUserProfile(userId);
        user.setPhone(phoneDTO.getPhone());
        userMapper.update(user);
    }

    /**
     * 更新邮箱
     */
    @Transactional
    public void updateEmail(Long userId, EmailUpdateDTO emailDTO) {
        // 验证验证码
        verifyCode(emailDTO.getEmail(), "email", emailDTO.getCode());
        
        // 检查邮箱是否已被使用
        if (userMapper.findByEmail(emailDTO.getEmail()) != null) {
            throw new BusinessException("该邮箱已被使用");
        }
        
        // 更新邮箱
        User user = getUserProfile(userId);
        user.setEmail(emailDTO.getEmail());
        userMapper.update(user);
    }

    /**
     * 用户登录
     */
    public LoginResponse login(String username, String password) {
        // 检查账号是否被锁定
        String lockKey = LOGIN_LOCK_PREFIX + username;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(lockKey))) {
            throw new BusinessException("账号已被锁定，请稍后再试");
        }
        
        User user = userMapper.findByUsername(username);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 验证密码
        if (!BCrypt.checkpw(password, user.getPassword())) {
            // 记录登录失败次数
            String countKey = LOGIN_FAIL_COUNT_PREFIX + username;
            long failCount = redisTemplate.opsForValue().increment(countKey, 1);
            if (failCount == 1) {
                redisTemplate.expire(countKey, LOCK_TIME_MINUTES, TimeUnit.MINUTES);
            }
            
            // 如果失败次数达到上限，锁定账号
            if (failCount >= MAX_FAIL_COUNT) {
                redisTemplate.opsForValue().set(lockKey, true, LOCK_TIME_MINUTES, TimeUnit.MINUTES);
                redisTemplate.delete(countKey);
                throw new BusinessException("密码错误次数过多，账号已被锁定" + LOCK_TIME_MINUTES + "分钟");
            }
            
            throw new BusinessException("密码错误，还剩" + (MAX_FAIL_COUNT - failCount) + "次机会");
        }

        // 登录成功，清除失败记录
        redisTemplate.delete(LOGIN_FAIL_COUNT_PREFIX + username);
        
        // 更新登录信息
        user.setLastLoginTime(System.currentTimeMillis());
        user.setLastLoginIp(getClientIp());
        user.setLoginCount(user.getLoginCount() + 1);
        userMapper.update(user);

        String token = jwtService.generateToken(username);
        return new LoginResponse(token, user.getUsername(), user.getNickname(), user.getAvatar());
    }

    /**
     * 删除用户
     */
    @Transactional
    public void deleteUser(Long userId) {
        User user = getUserProfile(userId);
        userMapper.deleteById(userId);
    }

    /**
     * 检查用户是否为管理员
     */
    public boolean isAdmin(Long userId) {
        User user = getUserProfile(userId);
        return "ADMIN".equals(user.getRole());
    }

    private String getClientIp() {
        // 实际项目中需要从请求上下文中获取
        return "127.0.0.1";
    }
}
