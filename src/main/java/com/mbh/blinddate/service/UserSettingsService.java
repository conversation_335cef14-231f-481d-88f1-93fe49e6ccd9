package com.mbh.blinddate.service;

import cn.hutool.crypto.digest.BCrypt;
import com.mbh.blinddate.entity.User;
import com.mbh.blinddate.entity.UserSettings;
import com.mbh.blinddate.exception.BusinessException;
import com.mbh.blinddate.mapper.UserSettingsMapper;
import com.mbh.blinddate.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class UserSettingsService {
    
    private final UserSettingsMapper userSettingsMapper;
    private final UserMapper userMapper;
    private final UserService userService;
    
    /**
     * 获取用户设置
     */
    public UserSettings getUserSettings(Long userId) {
        return userSettingsMapper.findByUserId(userId);
    }
    
    /**
     * 创建默认设置
     */
    @Transactional
    public UserSettings createDefaultSettings(Long userId) {
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        UserSettings settings = new UserSettings();
        settings.setUserId(userId);
        userSettingsMapper.insert(settings);
        return settings;
    }
    
    /**
     * 更新用户设置
     */
    @Transactional
    public UserSettings updateSettings(Long userId, UserSettings newSettings) {
        UserSettings settings = getUserSettings(userId);
        if (settings == null) {
            settings = new UserSettings();
            settings.setUserId(userId);
        }
        
        settings.setMinAge(newSettings.getMinAge());
        settings.setMaxAge(newSettings.getMaxAge());
        settings.setPreferredGender(newSettings.getPreferredGender());
        settings.setPreferredLocation(newSettings.getPreferredLocation());
        
        if (settings.getId() == null) {
            userSettingsMapper.insert(settings);
        } else {
            userSettingsMapper.update(settings);
        }
        return settings;
    }
    
    /**
     * 删除用户设置
     */
    @Transactional
    public void deleteUserSettings(Long userId) {
        UserSettings settings = getUserSettings(userId);
        if (settings != null) {
            userSettingsMapper.deleteById(settings.getId());
        }
    }

    /**
     * 删除账号
     */
    @Transactional
    public void deleteAccount(Long userId, String password) {
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 验证密码
        if (!BCrypt.checkpw(password, user.getPassword())) {
            throw new BusinessException("密码错误");
        }

        // 删除用户设置
        deleteUserSettings(userId);

        // 删除用户
        userMapper.deleteById(userId);
    }
}
