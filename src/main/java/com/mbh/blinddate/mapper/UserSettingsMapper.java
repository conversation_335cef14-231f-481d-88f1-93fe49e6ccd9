package com.mbh.blinddate.mapper;

import com.mbh.blinddate.entity.UserSettings;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserSettingsMapper {
    UserSettings findById(@Param("id") Long id);
    UserSettings findByUserId(@Param("userId") Long userId);
    void insert(UserSettings userSettings);
    void update(UserSettings userSettings);
    void deleteById(@Param("id") Long id);
}
