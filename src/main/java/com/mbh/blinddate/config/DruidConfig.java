package com.mbh.blinddate.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.support.http.StatViewServlet;
import com.alibaba.druid.support.http.WebStatFilter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * Druid数据源配置
 * 监控页面访问地址：http://localhost:8080/api/druid/
 * 用户名：admin 密码：admin123
 * 
 * <AUTHOR>
 */
@Configuration
public class DruidConfig {

    /**
     * 配置Druid数据源
     */
    @ConfigurationProperties(prefix = "spring.datasource")
    @Bean
    public DataSource druidDataSource() {
        return new DruidDataSource();
    }

    /**
     * 配置Druid的监控页面
     */
    @Bean
    public ServletRegistrationBean<StatViewServlet> statViewServlet() {
        ServletRegistrationBean<StatViewServlet> bean = new ServletRegistrationBean<>(new StatViewServlet(), "/druid/*");
        
        Map<String, String> initParams = new HashMap<>();
        // 监控页面登录用户名和密码
        initParams.put("loginUsername", "admin");
        initParams.put("loginPassword", "admin123");
        // 默认允许所有访问
        initParams.put("allow", "");
        // 拒绝访问的IP，deny优先于allow
        initParams.put("deny", "");
        
        bean.setInitParameters(initParams);
        return bean;
    }

    /**
     * 配置web监控的filter
     */
    @Bean
    public FilterRegistrationBean<WebStatFilter> webStatFilter() {
        FilterRegistrationBean<WebStatFilter> bean = new FilterRegistrationBean<>();
        bean.setFilter(new WebStatFilter());
        
        Map<String, String> initParams = new HashMap<>();
        // 不统计这些请求数据
        initParams.put("exclusions", "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*");
        
        bean.setInitParameters(initParams);
        // 设置拦截器的映射路径
        bean.setUrlPatterns(Arrays.asList("/*"));
        
        return bean;
    }
}
