package com.mbh.blinddate.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 缓存配置属性
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "cache")
public class CacheProperties {

    private Caffeine caffeine = new Caffeine();
    private UserCache user = new UserCache();
    private ConfigCache config = new ConfigCache();

    @Data
    public static class Caffeine {
        private long maximumSize = 10000;
        private long expireAfterWriteMinutes = 10;
        private long expireAfterAccessMinutes = 5;
        private int initialCapacity = 100;
    }

    @Data
    public static class UserCache {
        private long maximumSize = 5000;
        private long expireAfterWriteMinutes = 5;
    }

    @Data
    public static class ConfigCache {
        private long maximumSize = 1000;
        private long expireAfterWriteHours = 1;
    }
}
