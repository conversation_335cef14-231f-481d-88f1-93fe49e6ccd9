package com.mbh.blinddate.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 异步任务配置属性
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "async")
public class AsyncProperties {

    private TaskPool task = new TaskPool();
    private EmailPool email = new EmailPool();
    private FilePool file = new FilePool();

    @Data
    public static class TaskPool {
        private int corePoolSize = 8;
        private int maxPoolSize = 16;
        private int queueCapacity = 100;
        private int keepAliveSeconds = 60;
        private int awaitTerminationSeconds = 60;
    }

    @Data
    public static class EmailPool {
        private int corePoolSize = 2;
        private int maxPoolSize = 5;
        private int queueCapacity = 50;
    }

    @Data
    public static class FilePool {
        private int corePoolSize = 4;
        private int maxPoolSize = 8;
        private int queueCapacity = 200;
    }
}
