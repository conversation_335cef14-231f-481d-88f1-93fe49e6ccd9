package com.mbh.blinddate.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

/**
 * 缓存配置类
 * 使用Caffeine替代默认的ConcurrentHashMap，提供过期机制
 * 
 * <AUTHOR>
 */
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * 配置Caffeine缓存管理器
     */
    @Bean
    @Primary
    public CacheManager caffeineCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        
        // 配置缓存属性
        cacheManager.setCaffeine(Caffeine.newBuilder()
                // 最大缓存数量
                .maximumSize(10000)
                // 写入后过期时间
                .expireAfterWrite(10, TimeUnit.MINUTES)
                // 访问后过期时间
                .expireAfterAccess(5, TimeUnit.MINUTES)
                // 初始容量
                .initialCapacity(100)
                // 启用统计
                .recordStats());
                
        // 设置缓存名称
        cacheManager.setCacheNames(java.util.Arrays.asList(
                "userCache",      // 用户缓存
                "tokenCache",     // Token缓存
                "configCache",    // 配置缓存
                "matchCache",     // 匹配缓存
                "messageCache"    // 消息缓存
        ));
        
        return cacheManager;
    }

    /**
     * 用户信息缓存 - 短期缓存
     */
    @Bean("userCacheManager")
    public CacheManager userCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager("userCache");
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(5000)
                .expireAfterWrite(5, TimeUnit.MINUTES)
                .recordStats());
        return cacheManager;
    }

    /**
     * 配置信息缓存 - 长期缓存
     */
    @Bean("configCacheManager")
    public CacheManager configCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager("configCache");
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .recordStats());
        return cacheManager;
    }
}
