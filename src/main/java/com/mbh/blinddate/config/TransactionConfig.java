package com.mbh.blinddate.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.TransactionManagementConfigurer;

import javax.sql.DataSource;

/**
 * 事务配置类
 * 优化事务管理，避免长事务持锁影响并发
 * 
 * <AUTHOR>
 */
@Configuration
@EnableTransactionManagement
public class TransactionConfig implements TransactionManagementConfigurer {

    private final DataSource dataSource;

    // 事务配置参数
    @Value("${transaction.default-timeout:30}")
    private int defaultTimeout;

    @Value("${transaction.global-rollback-on-participation-failure:false}")
    private boolean globalRollbackOnParticipationFailure;

    public TransactionConfig(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    @Bean
    @Override
    public PlatformTransactionManager annotationDrivenTransactionManager() {
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager();
        transactionManager.setDataSource(dataSource);
        // 从配置文件读取事务超时时间
        transactionManager.setDefaultTimeout(defaultTimeout);
        // 设置事务同步
        transactionManager.setGlobalRollbackOnParticipationFailure(globalRollbackOnParticipationFailure);
        return transactionManager;
    }
}
