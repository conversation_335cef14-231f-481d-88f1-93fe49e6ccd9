package com.mbh.blinddate.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import java.time.LocalDateTime;

@Data
public class User {
    private Long id;
    private String username;
    
    @JsonIgnore
    private String password;
    private String email;
    private String phone;
    private String nickname;
    private String avatar;
    private String gender;
    private LocalDateTime birthday;
    private String location;
    private String bio;
    private Long lastLoginTime;
    private String lastLoginIp;
    private Integer loginCount;
    private Integer status;
    private String role;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
