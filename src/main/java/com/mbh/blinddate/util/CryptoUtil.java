package com.mbh.blinddate.util;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

@Component
@ConditionalOnProperty(prefix = "crypto", name = "enabled", havingValue = "true", matchIfMissing = true)
public class CryptoUtil {

    @Value("${crypto.sm2.private-key:}")
    private String sm2PrivateKey;

    @Value("${crypto.sm2.public-key:}")
    private String sm2PublicKey;

    private static final long TIMESTAMP_VALIDITY = 5 * 60 * 1000; // 5分钟

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    /**
     * SM2解密
     */
    public String sm2Decrypt(String encryptedData) {
        try {
            // 如果密钥未配置，返回原文（用于开发测试）
            if (sm2PrivateKey.isEmpty()) {
                return encryptedData;
            }

            byte[] privateKeyBytes = Base64.getDecoder().decode(sm2PrivateKey);
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("EC", "BC");
            PrivateKey privateKey = keyFactory.generatePrivate(pkcs8KeySpec);

            Cipher cipher = Cipher.getInstance("SM2", "BC");
            cipher.init(Cipher.DECRYPT_MODE, privateKey);

            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            return new String(decryptedBytes);
        } catch (Exception e) {
            throw new RuntimeException("SM2解密失败", e);
        }
    }

    /**
     * SM4解密
     */
    public String sm4Decrypt(String encryptedData, String key) {
        try {
            // 如果密钥未配置，返回原文（用于开发测试）
            if (key == null || key.isEmpty()) {
                return encryptedData;
            }

            byte[] keyBytes = Base64.getDecoder().decode(key);
            SecretKeySpec sm4Key = new SecretKeySpec(keyBytes, "SM4");

            Cipher cipher = Cipher.getInstance("SM4/ECB/PKCS5Padding", "BC");
            cipher.init(Cipher.DECRYPT_MODE, sm4Key);

            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            return new String(decryptedBytes);
        } catch (Exception e) {
            throw new RuntimeException("SM4解密失败", e);
        }
    }

    /**
     * AES解密
     */
    public String aesDecrypt(String encryptedData, String key) {
        try {
            // 如果密钥未配置，返回原文（用于开发测试）
            if (key == null || key.isEmpty()) {
                return encryptedData;
            }

            // 将十六进制字符串密钥转换为字节数组
            byte[] keyBytes = hexStringToByteArray(key);
            SecretKeySpec secretKey = new SecretKeySpec(keyBytes, "AES");

            // 使用 AES/ECB/PKCS5Padding 模式
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);

            // 解密 Base64 编码的数据
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            return new String(decryptedBytes, "UTF-8");
        } catch (Exception e) {
            throw new RuntimeException("AES解密失败: " + e.getMessage(), e);
        }
    }

    // 辅助方法：将十六进制字符串转换为字节数组
    private byte[] hexStringToByteArray(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                                 + Character.digit(hex.charAt(i+1), 16));
        }
        return data;
    }

    /**
     * 验证时间戳是否有效
     */
    public boolean isTimestampValid(long timestamp) {
        long currentTime = System.currentTimeMillis();
        return Math.abs(currentTime - timestamp) <= TIMESTAMP_VALIDITY;
    }
}
