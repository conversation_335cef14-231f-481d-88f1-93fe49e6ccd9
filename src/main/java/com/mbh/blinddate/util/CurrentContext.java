package com.mbh.blinddate.util;

public class CurrentContext {
    
    private static final ThreadLocal<Long> userIdHolder = new ThreadLocal<>();
    
    public static void setCurrentUserId(Long userId) {
        userIdHolder.set(userId);
    }
    
    public static Long getCurrentUserId() {
        return userIdHolder.get();
    }
    
    public static void clear() {
        userIdHolder.remove();
    }
}
