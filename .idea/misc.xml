<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State />
          <State>
            <id>Abstraction issuesJava</id>
          </State>
          <State>
            <id>Android</id>
          </State>
          <State>
            <id>Control flow issuesJava</id>
          </State>
          <State>
            <id>Google Web Toolkit</id>
          </State>
          <State>
            <id>Java</id>
          </State>
          <State>
            <id>LintAndroid</id>
          </State>
          <State>
            <id>LoggingJava</id>
          </State>
          <State>
            <id>MemoryJava</id>
          </State>
          <State>
            <id>PerformanceLintAndroid</id>
          </State>
          <State>
            <id>SecurityJava</id>
          </State>
          <State>
            <id>Serialization issuesJava</id>
          </State>
        </expanded-state>
        <selected-state>
          <State>
            <id>SerializableHasSerialVersionUIDField</id>
          </State>
        </selected-state>
      </profile-state>
    </entry>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" project-jdk-name="1.8" project-jdk-type="JavaSDK" />
</project>