<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="dataSourceStorageLocal" created-in="IU-253.17525.95">
    <data-source name="blind_date@60.205.163.57" uuid="e7c82d63-46c2-4407-b4f5-1c50139368f6">
      <database-info product="MariaDB" version="10.6.22-MariaDB" jdbc-version="4.2" driver-name="MariaDB Connector/J" driver-version="3.3.3" dbms="MARIADB" exact-version="10.6.22" exact-driver-version="3.3">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="exact" quoted-identifiers="exact" />
      <user-name>root</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema" qname="@" />
        </introspection-scope>
      </schema-mapping>
    </data-source>
    <data-source name="0@60.205.163.57" uuid="d51f8af0-e917-420d-9ee6-4651fb0584f8">
      <database-info product="Redis Standalone" version="7.2.8" jdbc-version="4.2" driver-name="Redis JDBC Driver" driver-version="1.5" dbms="REDIS" exact-version="7.2.8" exact-driver-version="1.5">
        <jdbc-catalog-is-schema>true</jdbc-catalog-is-schema>
      </database-info>
      <case-sensitivity plain-identifiers="mixed" quoted-identifiers="mixed" />
      <user-name>default</user-name>
      <schema-mapping />
    </data-source>
  </component>
</project>